package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.AutogptLog
import com.wonderslate.logs.AutogptErrorLoggerService
import com.wonderslate.sqlutil.SafeSql
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.grails.web.json.JSONObject
import org.springframework.transaction.annotation.Propagation


class AutogptService {

    DataProviderService dataProviderService
    def grailsApplication
    PromptService promptService
    AutogptErrorLoggerService autogptErrorLoggerService
    TheoryBooksService theoryBooksService

    // ==================== COMMON UTILITY METHODS ====================

    /**
     * Validates and prepares resource data with common error checking
     */
    private Map validateAndPrepareResource(params) {
        if (!params.resId) {
            return [status: "ERROR", message: "resId parameter is required"]
        }

        ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        if (!resourceDtl) {
            return [status: "ERROR", message: "Resource not found for resId: " + params.resId]
        }

        ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
        if (!chaptersMst) {
            return [status: "ERROR", message: "Chapter not found for chapterId: " + resourceDtl.chapterId]
        }

        return [status: "OK", resourceDtl: resourceDtl, chaptersMst: chaptersMst]
    }

    /**
     * Builds extract path with fallback logic
     */
    private String buildExtractPath(ChaptersMst chaptersMst, ResourceDtl resourceDtl) {
        String extractPath = resourceDtl.extractPath
        if (!extractPath || extractPath.trim().isEmpty()) {
            extractPath = "supload/pdfextracts/${chaptersMst.bookId}/${chaptersMst.id}/${resourceDtl.id}/extractedImages/${resourceDtl.id}.txt"
        }
        return extractPath
    }

    /**
     * Ensures vector file exists, creates if missing
     */
    private void ensureVectorFileExists(params, String extractPath) {
        File sourceFile = new File(grailsApplication.config.grails.basedir.path + "/" + extractPath)
        if (!sourceFile.exists()) {
            storePdfVectors(params)
        }
    }

    /**
     * Gets full file path from extract path
     */
    private String getFullFilePath(String extractPath) {
        return grailsApplication.config.grails.basedir.path + "/" + extractPath
    }

    /**
     * Gets folder path from extract path
     */
    private String getFolderPath(String extractPath) {
        return extractPath.substring(0, extractPath.lastIndexOf("/"))
    }

    // ==================== JSON PROCESSING UTILITIES ====================

    /**
     * Safely parses JSON response with error handling and logging
     */
    private List parseJsonResponseSafely(String response, String operation, ResourceDtl resourceDtl) {
        if (!response || response.trim().isEmpty() || "[]\\n[]\\n".equals(response)) {
            return []
        }

        try {
            return new JsonSlurper().parseText(jsonCleaner(response))
        } catch (Exception e) {
            String truncatedOperation = truncatePromptType(operation)
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedOperation,
                "Exception in parsing JSON: ${e.toString()}", response)

            // Try to fix JSON and parse again
            try {
                String fixedResponse = fixJSONFromLLM(response)
                return new JsonSlurper().parseText(jsonCleaner(fixedResponse))
            } catch (Exception e1) {
                String truncatedOperationFixed = truncatePromptType("${operation}Fixed")
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedOperationFixed,
                    "Exception in parsing fixed JSON: ${e1.toString()}", response)
                return []
            }
        }
    }

    /**
     * Processes LLM response with error handling and retry logic
     */
    private String processLLMResponseSafely(ResourceDtl resourceDtl, String inputText, String promptType, String serverIPAddress) {
        String response = getLLMResponse(resourceDtl, inputText, promptType, serverIPAddress)

        if (response?.startsWith("Error-")) {
            String errorMessage = response.substring(6)
            String truncatedPromptType = truncatePromptType(promptType)
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedPromptType, errorMessage, inputText)

            // Try with fixed input
            try {
                String fixedInput = fixJSONFromLLM(inputText)
                response = getLLMResponse(resourceDtl, fixedInput, promptType, serverIPAddress)
            } catch (Exception e) {
                String truncatedPromptTypeFixed = truncatePromptType("${promptType}Fixed")
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedPromptTypeFixed,
                    "Exception in getLLMResponse", inputText)
                return null
            }
        }

        return response
    }

    /**
     * Truncates prompt type to fit database column constraints
     */
    private String truncatePromptType(String promptType) {
        // Assuming prompt_type column has a limit (commonly 50 or 100 characters)
        int maxLength = 50
        return promptType?.length() > maxLength ? promptType.substring(0, maxLength) : promptType
    }

    /**
     * Safely truncates exception messages for logging
     */
    private String truncateException(Exception e, int maxLength = 1000) {
        String message = e.toString()
        return message.length() > maxLength ? message.substring(0, maxLength) : message
    }

    // ==================== QUESTION PROCESSING UTILITIES ====================

    /**
     * Creates or gets ResourceDtl for question storage
     */
    private ResourceDtl getOrCreateQuestionResource(ResourceDtl sourceResource, String resourceName, String resType, String gptResourceType = null) {
        ResourceDtl existingResource = ResourceDtl.findByChapterIdAndResourceName(sourceResource.chapterId, resourceName)

        if (existingResource) {
            return existingResource
        }

        QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
        quizIdGenerator.save()

        ResourceDtl newResource = new ResourceDtl()
        newResource.resLink = quizIdGenerator.id
        newResource.createdBy = "System"
        newResource.resType = resType
        newResource.chapterId = sourceResource.chapterId
        newResource.resourceName = resourceName
        if (gptResourceType) {
            newResource.gptResourceType = gptResourceType
        }
        newResource.save(failOnError: true, flush: true)

        return newResource
    }

    /**
     * Saves question to database with language and formula handling
     */
    private void saveQuestionToDatabase(Map questionData, ResourceDtl targetResource, BooksMst booksMst, String questionType) {
        ObjectiveMst om

        if (questionType == "MCQ") {
            // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
            String correctAnswerStr = questionData.correctAnswer.toString()
            boolean isOption1 = correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")
            boolean isOption2 = correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")
            boolean isOption3 = correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")
            boolean isOption4 = correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")

            om = new ObjectiveMst(
                quizId: new Integer(targetResource.resLink),
                quizType: "MCQ",
                question: fixFormulas(questionData.questionText ?: questionData.question),
                answer: fixFormulas(questionData.solution),
                difficultylevel: questionData.difficultyLevel,
                qType: questionData.questionType,
                answerDescription: fixFormulas(questionData.explanation),
                bloomType: questionData.bloomLevel,
                option1: questionData.option1,
                option2: questionData.option2,
                option3: questionData.option3,
                option4: questionData.option4,
                answer1: isOption1 ? "Yes" : null,
                answer2: isOption2 ? "Yes" : null,
                answer3: isOption3 ? "Yes" : null,
                answer4: isOption4 ? "Yes" : null
            )
        } else {
            om = new ObjectiveMst(
                quizId: new Integer(targetResource.resLink),
                quizType: "QA",
                question: fixFormulas(questionData.question),
                answer: fixFormulas(questionData.solution),
                difficultylevel: questionData.difficultyLevel,
                qType: questionData.questionType,
                answerDescription: fixFormulas(questionData.explanation),
                bloomType: questionData.bloomLevel
            )
        }

        om.save(failOnError: true, flush: true)

        // Handle language corrections
        if (booksMst?.language && !"English".equals(booksMst.language) && !"".equals(booksMst.language)) {
            fixLanguage("${om.id}", questionType.toLowerCase(), booksMst.language)
        }

        // Handle formula corrections
        if ("true".equals("${questionData.hasFormula}")) {
            fixQuestion("${om.id}", questionType.toLowerCase())
        }
    }

    /**
     * Processes questions in batches with a given processor function
     */
    private void processQuestionsInBatches(List questions, int batchSize, Closure processor) {
        int currentIndex = 0
        int totalQuestions = questions.size()

        while (currentIndex < totalQuestions) {
            int endIndex = Math.min(currentIndex + batchSize, totalQuestions)
            List batch = questions.subList(currentIndex, endIndex)

            processor.call(batch, currentIndex)
            currentIndex = endIndex
        }
    }

    /**
     * Checks if exercise is already created for a chapter
     */
    private boolean isExerciseAlreadyCreated(Long chapterId) {
        ResourceDtl exerciseResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chapterId, "Exercise Solutions")
        if (exerciseResourceDtl) {
            try {
                ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(exerciseResourceDtl.resLink))
                return objectiveMst != null
            } catch (Exception e) {
                return false
            }
        }
        println("Exercise not created for chapter ${chapterId}")
        return false
    }

    /**
     * Processes file content in chunks and extracts exercises
     */
    private int processFileInChunks(File textFile, ResourceDtl resourceDtl, BooksMst booksMst, String serverIPAddress) {
        int totalQuestions = 0
        int chunkSize = 20000
        ResourceDtl exerciseResourceDtl = null

        textFile.withReader('UTF-8') { reader ->
            char[] buffer = new char[chunkSize]
            StringBuilder currentChunk = new StringBuilder()
            int charsRead

            while ((charsRead = reader.read(buffer, 0, chunkSize)) != -1) {
                currentChunk.append(buffer, 0, charsRead)

                // If we have read exactly chunkSize characters, look for next paragraph break
                if (charsRead == chunkSize) {
                    int nextChar
                    while ((nextChar = reader.read()) != -1) {
                        currentChunk.append((char) nextChar)
                        String content = currentChunk.toString()
                        if (content.endsWith("\n\n") || content.endsWith("\r\n\r\n")) {
                            break
                        }
                    }
                }

                List response = extractExercisesFromChunk(resourceDtl, currentChunk.toString(), serverIPAddress)

                if (response && response.size() > 0) {
                    if (!exerciseResourceDtl) {
                        exerciseResourceDtl = getOrCreateQuestionResource(resourceDtl, "Exercise Solutions", "QA")
                    }

                    totalQuestions += processExtractedExercises(response, exerciseResourceDtl, resourceDtl, booksMst, serverIPAddress)
                }

                currentChunk.setLength(0) // Clear the buffer for next chunk
            }
        }

        return totalQuestions
    }

    /**
     * Extracts exercises from a text chunk
     */
    private List extractExercisesFromChunk(ResourceDtl resourceDtl, String inputText, String serverIPAddress) {
        Prompts prompts = Prompts.findByPromptType("exerciseExtractor")
        String response = processLLMResponseSafely(resourceDtl, inputText, prompts.basePrompt, serverIPAddress)

        if (!response) {
            return []
        }

        return parseJsonResponseSafely(response, "exerciseExtractor", resourceDtl)
    }

    /**
     * Processes extracted exercises and saves them to database
     */
    private int processExtractedExercises(List exercises, ResourceDtl exerciseResourceDtl, ResourceDtl sourceResourceDtl, BooksMst booksMst, String serverIPAddress) {
        int totalQuestions = 0
        int noOfQuestionsPerIteration = 5

        processQuestionsInBatches(exercises, noOfQuestionsPerIteration) { batch, startIndex ->
            String inputQuestions = "The questions are \n"
            batch.eachWithIndex { exercise, index ->
                inputQuestions += "${startIndex + index + 1}. ${exercise.question}\n"
            }

            inputQuestions = jsonCleaner(inputQuestions)
            def jsonAnswer = getSolution(sourceResourceDtl, inputQuestions, sourceResourceDtl.chapterId, booksMst.id, "solutionCreator", 1, serverIPAddress)

            if (jsonAnswer?.answer) {
                List solutions = parseJsonResponseSafely(jsonAnswer.answer, "solutionCreator", sourceResourceDtl)
                solutions.each { solution ->
                    saveQuestionToDatabase(solution, exerciseResourceDtl, booksMst, "QA")
                    totalQuestions++
                }
            }
        }

        return totalQuestions
    }

    def getChapterMetaData(params) {
        // Validate and prepare resources
        Map validation = validateAndPrepareResource(params)
        if (validation.status == "ERROR") {
            return validation
        }

        ResourceDtl resourceDtl = validation.resourceDtl
        ChaptersMst chaptersMst = validation.chaptersMst

        String extractPath = buildExtractPath(chaptersMst, resourceDtl)
        String folderPath = getFolderPath(extractPath)
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/chapterMetadata${chaptersMst.id}.txt")
        //if file exists, delete it and create a new one
        if (metadataFile.exists()) {
            println("Metadata file exists for chapter ${chaptersMst.id} and we will delete it first")
            metadataFile.delete()
        }
             metadataFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/chapterMetadata${chaptersMst.id}.txt")
            ensureVectorFileExists(params, extractPath)
            String filePath = getFullFilePath(extractPath)
            File textFile = new File(filePath)
            String fileContent = textFile.text

            Prompts prompts = Prompts.findByPromptType("chapterMetadataExtractor")
            String response = getLLMResponse(resourceDtl, fileContent, prompts.basePrompt, params.serverIPAddress)
            metadataFile.write(response)


        return [status: "OK"]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def exerciseCollector(params) {
        try {
            // Validate and prepare resources
            Map validation = validateAndPrepareResource(params)
            if (validation.status == "ERROR") {
                return validation
            }

            ResourceDtl resourceDtl = validation.resourceDtl
            ChaptersMst chaptersMst = validation.chaptersMst

            // Check if exercises already exist
            if (isExerciseAlreadyCreated(chaptersMst.id)) {
                return [status: "OK", message: "Exercises already created"]
            }

            BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
            String extractPath = buildExtractPath(chaptersMst, resourceDtl)
            ensureVectorFileExists(params, extractPath)

            String filePath = getFullFilePath(extractPath)
            File textFile = new File(filePath)

            if (!textFile.exists()) {
                return [status: "ERROR", message: "Text file not found at path: " + filePath]
            }

            int totalQuestions = processFileInChunks(textFile, resourceDtl, booksMst, params.serverIPAddress)

            dataProviderService.getChaptersList(chaptersMst.bookId)
            return [status: "OK", message: "File processed successfully", totalQuestions: totalQuestions]

        } catch (Exception e) {
            println("Exception in exerciseCollector: ${truncateException(e)}")
            return [status: "ERROR", message: "Error processing file: " + e.message]
        }
    }

    def examplesAndExercisesExtractor(ResourceDtl resourceDtl, String inputText, String serverIPAddress) {
        try {
            Prompts prompts = Prompts.findByPromptType("exerciseExtractor")
            String response = processLLMResponseSafely(resourceDtl, inputText, prompts.basePrompt, serverIPAddress)

            if (!response) {
                return []
            }

            return parseJsonResponseSafely(response, "exerciseExtractor", resourceDtl)
        } catch (Exception e) {
            println("Exception in examplesAndExercisesExtractor: ${truncateException(e)}")
            return []
        }
    }

    def getLLMResponse(ResourceDtl resourceDtl, String inputText, String basePrompt, String serverIPAddress = null) {
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        String extractPath = buildExtractPath(chaptersMst, resourceDtl)
        String folderPath = getFolderPath(extractPath)

        File tempFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/extractData${resourceDtl.id}.txt")

        try {
            // Clean up existing file and write new content
            if (tempFile.exists()) {
                tempFile.delete()
            }
            tempFile.write(inputText, "UTF-8")

            // Make HTTP request
            String response = makeMultipartRequest(serverIPAddress, basePrompt, tempFile)
            def json = new JsonSlurper().parseText(response)
            String responseAnswer = jsonCleaner(json.response)

            return responseAnswer
        } catch (Exception e) {
            println("Exception in getLLMResponse: ${truncateException(e)}")
            return "Error-${e.getMessage()}"
        } finally {
            // Always clean up temp file
            if (tempFile.exists()) {
                tempFile.delete()
            }
        }
    }

    /**
     * Makes multipart HTTP request with optimized connection handling
     */
    private String makeMultipartRequest(String serverIPAddress, String basePrompt, File file) {
        URL url = new URL("http://${serverIPAddress}:8000/api/retrieveDataAdminText")
        HttpURLConnection conn = (HttpURLConnection) url.openConnection()

        try {
            conn.setRequestMethod("POST")
            conn.setDoOutput(true)
            conn.setDoInput(true)

            String boundary = "===${System.currentTimeMillis()}==="
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=${boundary}")

            String prompt = fixCurlyBrackets(basePrompt)

            conn.getOutputStream().withWriter("UTF-8") { writer ->
                // Write prompt part
                writer.write("--${boundary}\r\n")
                writer.write("Content-Disposition: form-data; name=\"prompt\"\r\n")
                writer.write("Content-Type: text/plain; charset=UTF-8\r\n\r\n")
                writer.write(prompt)
                writer.write("\r\n")

                // Write file part
                writer.write("--${boundary}\r\n")
                writer.write("Content-Disposition: form-data; name=\"file\"; filename=\"file.txt\"\r\n")
                writer.write("Content-Type: text/plain; charset=UTF-8\r\n\r\n")
                writer.write(file.text)
                writer.write("\r\n")

                // End boundary
                writer.write("--${boundary}--\r\n")
                writer.flush()
            }

            // Check response code before reading response
            int responseCode = conn.getResponseCode()
            if (responseCode == 200) {
                return conn.getInputStream().getText("UTF-8")
            } else {
                // Read error stream for better error reporting
                String errorResponse = ""
                try {
                    errorResponse = conn.getErrorStream()?.getText("UTF-8") ?: "No error details available"
                } catch (Exception e) {
                    errorResponse = "Could not read error response: ${e.message}"
                }
                throw new IOException("Server returned HTTP response code: ${responseCode}. Error: ${errorResponse}")
            }
        } finally {
            conn.disconnect()
        }
    }

    def getSolution(ResourceDtl resourceDtl, question, chapterId, bookId, String promptName, int noOfSimilarQuestions = 1, String serverIPAddress = null) {
        try {
            // Build custom prompt
            String customPrompt = buildCustomPrompt(promptName, bookId, noOfSimilarQuestions)

            // Build request body
            Map requestBody = [
                namespace: resourceDtl.vectorStored,
                resId: resourceDtl.id,
                resType: "userInput",
                chatHistory: "",
                query: question,
                chapterId: chapterId,
                bookId: bookId,
                customPrompt: customPrompt
            ]

            // Make HTTP request
            return makeJsonRequest(serverIPAddress, requestBody, resourceDtl, promptName, question)
        } catch (Exception e) {
            println("Exception in getSolution: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "Exception in getSolution: ${e.getMessage()}", question)
            return null
        }
    }

    /**
     * Builds custom prompt based on prompt type and parameters
     */
    private String buildCustomPrompt(String promptName, Long bookId, int noOfSimilarQuestions) {
        Prompts prompt = Prompts.findByPromptType(promptName)
        String customPrompt = prompt.basePrompt

        // Add language-specific instructions
        if (promptName in ["additionQuestionsCreator", "questionBankBuilder"]) {
            BooksMst booksMst = dataProviderService.getBooksMst(bookId)
            if (booksMst?.language && !"English".equals(booksMst.language) && !"".equals(booksMst.language)) {
                customPrompt += " All questions created should be in ${booksMst.language} language."
            }
        }

        // Replace question count placeholder
        if ("additionQuestionsCreator".equals(promptName)) {
            customPrompt = customPrompt.replaceAll("NOOFQUESTIONS", "${noOfSimilarQuestions}")
        }

        return customPrompt
    }

    /**
     * Makes JSON HTTP request with optimized connection handling
     */
    private Map makeJsonRequest(String serverIPAddress, Map requestBody, ResourceDtl resourceDtl, String promptName, String question) {
        URL url = new URL("http://${serverIPAddress}:8000/api/retrieveDataForBook")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()

        try {
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write request body
            connection.getOutputStream().withWriter("UTF-8") { writer ->
                writer.write(new JsonBuilder(requestBody).toString())
            }

            int responseCode = connection.getResponseCode()
            if (responseCode == 200) {
                String response = connection.getInputStream().getText("UTF-8")
                def jsonResponse = new JsonSlurper().parseText(response)

                // Replace newlines with HTML breaks
                jsonResponse.answer = jsonResponse.answer.replaceAll("\\\\n", "<br>")
                return jsonResponse
            } else {
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "HTTP Error in getSolution: ${responseCode}", question)
                println("HTTP Error in getSolution: ${responseCode}")
                return null
            }
        } catch (Exception e) {
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "Exception in makeJsonRequest: ${e.getMessage()}", question)
            println("Exception in makeJsonRequest: ${truncateException(e)}")
            return null
        } finally {
            connection.disconnect()
        }
    }

    def removeExtraCommas(String json) {
        String cleaned = json.replaceAll(",\\s*,+", ","); // Replace multiple commas with a single comma
        cleaned = cleaned.replaceAll(",\\s*]", "]");      // Remove commas just before a closing bracket

        return cleaned;
    }

    def jsonCleaner(String jsonInput){
        jsonInput = jsonInput.replaceAll("`", "")
        jsonInput = jsonInput.replaceAll("json", "")
        jsonInput = jsonInput.replaceAll("\n", "")
        jsonInput = jsonInput.replaceAll("\r", "")
        //next i want to replace ][ with comma
        jsonInput = jsonInput.replaceAll("\\]\\[", ",")
        // replace },] with }]
        jsonInput = jsonInput.replaceAll("},]", "}]")

        //replace  .^ with blank
        jsonInput = jsonInput.replaceAll("\\.\\^", "")
        jsonInput = removeExtraCommas(jsonInput)
        jsonInput = fixJsonString(jsonInput)
        return jsonInput
    }

    def createAdditionalQuestions(ResourceDtl resourceDtl,ChaptersMst chaptersMst,questionList,String serverIPAddress = null){
        println("createAdditionalQuestions for chapter " + chaptersMst.id)
        String folderPath = "supload/pdfextracts/"+chaptersMst.bookId+"/"+chaptersMst.id+"/"+resourceDtl.id+"/extractedImages/"
        List<String> additionalQuestions = []
        int noOfSimilarQuestions = 1
        if(questionList.size()<200) {
            if (questionList.size() < 100) noOfSimilarQuestions = 2
            try {
                int noOfQuestionsPerIteration = 5
                int numberOfQuestions = questionList.size()
                int currentQuestionIndex = 0
                while (currentQuestionIndex < numberOfQuestions) {
                    String inputQuestions = "The subtopic questions are \n"

                    int noOfQuestionsForLoop = noOfQuestionsPerIteration
                    if (numberOfQuestions - currentQuestionIndex < noOfQuestionsPerIteration) {
                        noOfQuestionsForLoop = numberOfQuestions - currentQuestionIndex
                    }
                    for (int i = currentQuestionIndex; i < currentQuestionIndex + noOfQuestionsForLoop; i++) {
                        inputQuestions += (i + 1) + ". " + questionList[i] + "\n"
                    }
                    inputQuestions = jsonCleaner(inputQuestions)
                    def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "additionQuestionsCreator", noOfSimilarQuestions,serverIPAddress)
                    if (jsonAnswer != null) {
                        try {
                            def json1 = new JsonSlurper().parseText(jsonCleaner(jsonAnswer.answer))
                            json1.each { question1 ->
                                String questionText = (question1 instanceof String) ? question1 : question1.question?.toString()
                                if(questionText && !additionalQuestions.contains(questionText)) {
                                    additionalQuestions.add(questionText)
                                }
                            }
                        } catch (Exception e) {
                            println("Exception in questionBankBuilder2.1 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderAdditionalQuestions", "Exception in parsing jsonAnswer", jsonAnswer.answer)
                            String jsonAnswerCleaned = fixJSONFromLLM(jsonAnswer.answer)
                            try {
                                def json1 = new JsonSlurper().parseText(jsonAnswerCleaned)
                                json1.each { question1 ->
                                    String questionText = (question1 instanceof String) ? question1 : question1.question?.toString()
                                    if(questionText && !additionalQuestions.contains(questionText)) {
                                        additionalQuestions.add(questionText)
                                    }
                                }
                            } catch (Exception e1) {
                                 autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderAdditionalQuestionsFixed", "Exception in parsing jsonAnswer", jsonAnswerCleaned)
                            }
                        }
                    }
                    currentQuestionIndex += noOfQuestionsForLoop
                }
            } catch (Exception e) {
                println("Exception in questionBankBuilder1 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing responseAnswer", responseAnswer)
            }
        }
        println("Total additional questions are " + additionalQuestions.size()+" for chapter " + chaptersMst.id)
        if(additionalQuestions.size()>0){
            //add additional questions to question list
            questionList.addAll(additionalQuestions)

        }
        return questionList
    }

    def addAnswersToPYQs(ResourceDtl resourceDtl,ChaptersMst chaptersMst, ResourceDtl pyqResourceDtl, params){
        println("starting addAnswersToPYQs for chapter "+chaptersMst.id)
        ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(pyqResourceDtl.resLink))
        if(objectiveMst!=null) {
            List questionList = ObjectiveMst.findAllByQuizIdAndAnswerIsNull(new Integer(pyqResourceDtl.resLink))
            int noOfQuestionsPerIteration = 5
            List <String> solutionList = []

            println("Total questions without answers: ${questionList.size()} for chapter ${chaptersMst.id}")

            try {
                // Use the proven processQuestionsInBatches method to ensure all questions are processed
                processQuestionsInBatches(questionList, noOfQuestionsPerIteration) { batch, startIndex ->
                    String inputQuestions = "The questions are \n"
                    batch.eachWithIndex { question, index ->
                        inputQuestions += " ${question.id}. ${question.question}\n"
                    }

                    inputQuestions = jsonCleaner(inputQuestions)

                    try {
                        def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "solutionCreator", 1, params.serverIPAddress)
                        if (jsonAnswer?.answer) {
                            solutionList.add(jsonAnswer.answer)
                        }
                    } catch (Exception e) {
                        println("Exception in solutionCreator for chapter " + chaptersMst.id + " and the exception is " + e.toString())
                        autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreator", "Exception in solutionCreator", inputQuestions)

                        try {
                            String jsonAnswerCleaned = fixJSONFromLLM(inputQuestions)
                            def jsonAnswer = getSolution(resourceDtl, jsonAnswerCleaned, chaptersMst.id, chaptersMst.bookId, "solutionCreator", 1, params.serverIPAddress)
                            if (jsonAnswer?.answer) {
                                solutionList.add(jsonAnswer.answer)
                            }
                            println("Exception in solutionCreator for chapter " + chaptersMst.id + " and the exception is " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                        } catch (Exception e1) {
                            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreatorFixed", "Exception in solutionCreator", inputQuestions)
                        }
                    }
                }

                println("Generated solutions for ${solutionList.size()} batches for chapter ${chaptersMst.id}")

            } catch (Exception e) {
                println("Exception in parsing responseAnswer5 " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
            }

            //loop solutionList and add the questions and answers to ObjectiveMst
            println("Processing ${solutionList.size()} solution batches for chapter ${chaptersMst.id}")

            for(String solution:solutionList){
                def jsonAnswerList
                try {
                    try {
                        jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
                    } catch (Exception e) {
                        autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
                        solution = fixJSONFromLLM(solution)
                        jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
                    }

                    jsonAnswerList.each { json1 ->
                         ObjectiveMst om = ObjectiveMst.findById(new Integer("" + json1.questionNo))
                        if(om!=null){
                            om.answer = json1.solution
                            om.difficultylevel = json1.difficultyLevel
                            om.qType = json1.questionType
                            om.answerDescription = json1.explanation
                            om.bloomType = json1.bloomLevel
                            om.save(failOnError: true, flush: true)
                            if("true".equals(""+json1.hasFormula)){
                                fixQuestion(""+om.id, "subjective")
                            }
                        }
                    }
                }catch (Exception e){
                    autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
                }
            }

            println("Completed addAnswersToPYQs for chapter ${chaptersMst.id}")
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def questionBankBuilder(params) {
        // Validate and prepare resources
        Map validation = validateAndPrepareResource(params)
        if (validation.status == "ERROR") {
            return validation
        }

        ResourceDtl resourceDtl = validation.resourceDtl
        ChaptersMst chaptersMst = validation.chaptersMst
        println("questionBankBuilder for chapter inside questionBankBuilder ${chaptersMst.id}")
        // Handle PYQs if they exist
        handlePYQsIfNeeded(resourceDtl, chaptersMst, params)
        // Check if question bank already exists
        ResourceDtl questionBankResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(chaptersMst.id, "QuestionBank%")
        if (questionBankResourceDtl) {
            return [status: "OK", message: "Question bank already created", totalQuestions: "Not Counted"]
        }



        try {
            // Generate question bank
            List<String> questionList = generateQuestionBank(resourceDtl, chaptersMst, params)

            // Process and save questions
            List<String> solutionList = generateSolutionsForQuestions(questionList, resourceDtl, chaptersMst, params.serverIPAddress)
            addQuestionBank(resourceDtl, chaptersMst, solutionList)

            return [status: "OK", totalQuestions: questionList.size()]
        } catch (Exception e) {
            println("Exception in questionBankBuilder: ${truncateException(e)}")
            return [status: "ERROR", message: "Error in question bank builder: ${e.message}"]
        }
    }

    /**
     * Handles PYQs processing if needed
     */
    private void handlePYQsIfNeeded(ResourceDtl resourceDtl, ChaptersMst chaptersMst, params) {
        // Clear session cache to ensure we get fresh data from database
        ResourceDtl.withSession { session ->
            session.clear()
        }

        ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chaptersMst.id, "PYQs")
        println("pyqResourceDtl: ${pyqResourceDtl}"+" for chapter " + chaptersMst.id)

        if (pyqResourceDtl) {
            ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(pyqResourceDtl.resLink))
            if (objectiveMst) {
                //get number of questions which does not have answer
                int noOfQuestionsWithoutAnswer = ObjectiveMst.countByQuizIdAndAnswerIsNull(new Integer(pyqResourceDtl.resLink))
                println("noOfQuestionsWithoutAnswer: ${noOfQuestionsWithoutAnswer} for chapter " + chaptersMst.id)
                if (noOfQuestionsWithoutAnswer > 0) {
                    String extractPath = buildExtractPath(chaptersMst, resourceDtl)
                    ensureVectorFileExists(params, extractPath)
                    addAnswersToPYQs(resourceDtl, chaptersMst, pyqResourceDtl, params)
                }
                ObjectiveMst.withSession { session ->
                    session.clear()
                }
            }
        }
    }



    def addQuestionBank(ResourceDtl resourceDtl, ChaptersMst chaptersMst, List<String> solutionList) {
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        ResourceDtl mcqResourceDtl = null
        ResourceDtl qaResourceDtl = null

        for (String solution : solutionList) {
            List jsonAnswerList = parseJsonResponseSafely(solution, "questionBankBuilder", resourceDtl)

            jsonAnswerList.each { questionData ->
                if (questionData.questionType == "MCQ") {
                    if (!mcqResourceDtl) {
                        mcqResourceDtl = createMCQResource(resourceDtl)
                    }
                    saveQuestionToDatabase(questionData, mcqResourceDtl, booksMst, "MCQ")
                } else {
                    if (!qaResourceDtl) {
                        qaResourceDtl = createQAResource(resourceDtl)
                    }
                    saveQuestionToDatabase(questionData, qaResourceDtl, booksMst, "QA")
                }
            }
        }
    }

    /**
     * Creates MCQ resource with GPT default log
     */
    private ResourceDtl createMCQResource(ResourceDtl sourceResource) {
        ResourceDtl mcqResource = getOrCreateQuestionResource(sourceResource, "QuestionBank MCQs", "Multiple Choice Questions", "mcq")

        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(sourceResource.id, "mcq")
        if (!gptDefaultCreateLog) {
            gptDefaultCreateLog = new GptDefaultCreateLog(
                resId: mcqResource.id,
                promptType: "mcq",
                prompt: "Create MCQs (Multiple Choice Questions)",
                response: "MCQ",
                readingMaterialResId: sourceResource.id,
                username: "System",
                promptLabel: "Create MCQs (Multiple Choice Questions)"
            )
        } else {
            gptDefaultCreateLog.resId = mcqResource.id
        }
        gptDefaultCreateLog.save(failOnError: true, flush: true)

        return mcqResource
    }

    /**
     * Creates QA resource with GPT default log
     */
    private ResourceDtl createQAResource(ResourceDtl sourceResource) {
        ResourceDtl qaResource = getOrCreateQuestionResource(sourceResource, "QuestionBank QnA", "QA", "qna")

        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(sourceResource.id, "qna")
        if (!gptDefaultCreateLog) {
            gptDefaultCreateLog = new GptDefaultCreateLog(
                resId: qaResource.id,
                promptType: "qna",
                prompt: "Create Question & Answers",
                response: "QA",
                readingMaterialResId: sourceResource.id,
                username: "System",
                promptLabel: "Create Question & Answers"
            )
        } else {
            gptDefaultCreateLog.resId = qaResource.id
        }
        gptDefaultCreateLog.save(failOnError: true, flush: true)

        return qaResource
    }

    // ==================== QUESTION BANK BUILDER UTILITIES ====================

    /**
     * Loads and parses chapter metadata
     */
    private Map loadChapterMetadata(String folderPath, ChaptersMst chaptersMst, ResourceDtl resourceDtl) {
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/chapterMetadata${chaptersMst.id}.txt")
        String metadataString = metadataFile.text

        try {
            metadataString = jsonCleaner(metadataString)
            return new JsonSlurper().parseText(metadataString)
        } catch (Exception e) {
            println("Exception in questionBankBuilder metadata parsing: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing metadata", metadataString)

            metadataString = fixJSONFromLLM(metadataString)
            return new JsonSlurper().parseText(metadataString)
        }
    }

    /**
     * Generates questions for a subtopic
     */
    private List<String> generateQuestionsForSubtopic(ResourceDtl resourceDtl, String fileContent, def subtopic, Prompts prompts, String serverIPAddress, int subTopicNumber, Long chapterId) {
        List<String> questions = []

        try {
            println("Question generation started for subtopic ${subTopicNumber} for chapter ${chapterId}")
            String subTopicText = jsonCleaner(subtopic.toString())
            String promptText = "${prompts.basePrompt} \n${subTopicText}"
            promptText = jsonCleaner(promptText)

            String response = processLLMResponseSafely(resourceDtl, fileContent, promptText, serverIPAddress)
            println("Got the response for subtopic ${subTopicNumber} for chapter ${chapterId}")

            if (response) {
                List responseList = parseJsonResponseSafely(response, "questionBankBuilder", resourceDtl)
                responseList.each { questionItem ->
                    String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                    if (questionText && !questions.contains(questionText)) {
                        questions.add(questionText)
                    }
                }
            }

            println("Total questions are ${questions.size()} for subtopic ${subTopicNumber} for chapter ${chapterId}")
        } catch (Exception e) {
            println("Exception in questionBankBuilder subtopic processing: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in subtopic processing", e.toString())
        }

        return questions
    }

    /**
     * Generates MCQ questions for a subtopic
     */
    private List<String> generateMCQsForSubtopic(ResourceDtl resourceDtl, String fileContent, def subtopic, Prompts mcqPrompts, String serverIPAddress, int subTopicNumber, Long chapterId) {
        List<String> mcqQuestions = []

        try {
            String subTopicText = jsonCleaner(subtopic.toString())
            String promptText = "${mcqPrompts.basePrompt} \n${subTopicText}"
            println("Calling MCQ prompt for subtopic ${subTopicNumber} for chapter ${chapterId}")
            promptText = jsonCleaner(promptText)

            String response = processLLMResponseSafely(resourceDtl, fileContent, promptText, serverIPAddress)

            if (response) {
                List responseList = parseJsonResponseSafely(response, "questionBankBuilderMCQ", resourceDtl)
                println("Total MCQ questions are ${responseList.size()} for subtopic ${subTopicNumber} for chapter ${chapterId}")

                responseList.each { questionItem ->
                    String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                    if (questionText) {
                        mcqQuestions.add(questionText)
                    }
                }
            }

            println("Total questions after MCQ are ${mcqQuestions.size()} for subtopic ${subTopicNumber} for chapter ${chapterId}")
        } catch (Exception e) {
            println("Exception in MCQ generation: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderMCQ", "Exception in MCQ generation", e.toString())
        }

        return mcqQuestions
    }

    /**
     * Generates complete question bank for a chapter
     */
    private List<String> generateQuestionBank(ResourceDtl resourceDtl, ChaptersMst chaptersMst, params) {
        String extractPath = buildExtractPath(chaptersMst, resourceDtl)
        ensureVectorFileExists(params, extractPath)

        String folderPath = getFolderPath(extractPath)
        File questionBankFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/questionBank.txt")
        if (questionBankFile.exists()) {
            questionBankFile.delete()
        }

        // Load metadata
        Map metadata = loadChapterMetadata(folderPath, chaptersMst, resourceDtl)
        def subtopics = metadata.metadata.subtopics

        // Get prompts
        Prompts prompts = Prompts.findByPromptType("questionBankBuilder")
        Prompts mcqPrompts = Prompts.findByPromptType("questionBankBuilderMCQ")

        // Load file content
        String filePath = getFullFilePath(extractPath)
        File textFile = new File(filePath)
        String fileContent = textFile.text

        List<String> questionList = []
        int subTopicNumber = 1

        println("Number of subtopics are ${subtopics.size()} for chapter ${chaptersMst.id}")

        // Process each subtopic
        subtopics.each { subtopic ->
            // Generate regular questions
            List<String> subtopicQuestions = generateQuestionsForSubtopic(resourceDtl, fileContent, subtopic, prompts, params.serverIPAddress, subTopicNumber, chaptersMst.id)
            questionList.addAll(subtopicQuestions)

            // Generate MCQ questions
            List<String> mcqQuestions = generateMCQsForSubtopic(resourceDtl, fileContent, subtopic, mcqPrompts, params.serverIPAddress, subTopicNumber, chaptersMst.id)
            questionList.addAll(mcqQuestions)

            subTopicNumber++
        }

        println("Total questions are ${questionList.size()} for chapter ${chaptersMst.id}")

        // Add additional questions if needed
        if (questionList.size() < 200) {
            try {
                questionList = createAdditionalQuestions(resourceDtl, chaptersMst, questionList, params.serverIPAddress)
            } catch (Exception e) {
                println("Exception in createAdditionalQuestions for chapter ${chaptersMst.id}: ${truncateException(e)}")
            }
        }

        // Save question bank file
        questionBankFile.write(questionList.join("~~"))

        return questionList
    }

    /**
     * Generates solutions for a list of questions
     */
    private List<String> generateSolutionsForQuestions(List<String> questionList, ResourceDtl resourceDtl, ChaptersMst chaptersMst, String serverIPAddress) {
        List<String> solutionList = []
        int noOfQuestionsPerIteration = 5

        processQuestionsInBatches(questionList, noOfQuestionsPerIteration) { batch, startIndex ->
            String inputQuestions = "The questions are \n"
            batch.eachWithIndex { question, index ->
                inputQuestions += "${startIndex + index + 1}. ${question}\n"
            }

            inputQuestions = jsonCleaner(inputQuestions)

            try {
                def jsonAnswer = getSolution(resourceDtl, inputQuestions, chaptersMst.id, chaptersMst.bookId, "solutionCreator", 1, serverIPAddress)
                if (jsonAnswer?.answer) {
                    solutionList.add(jsonAnswer.answer)
                }
            } catch (Exception e) {
                println("Exception in solution generation: ${truncateException(e)}")
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "solutionCreator", "Exception in solution generation", inputQuestions)
            }
        }

        return solutionList
    }

    String fixJsonString(String inputJson) {
        // Replace LaTeX-specific sequences with properly escaped versions
        String fixedJson = inputJson
                .replace("\\\\\\\\", "FOURBACKSLASH")
                .replace("\\\\", "TWOBACKSLASH")
                .replace("\\", "ONEBACKSLASH")
        fixedJson = fixedJson.replace("FOURBACKSLASH", "\\\\")
                .replace("TWOBACKSLASH", "\\\\")
                .replace("ONEBACKSLASH", "\\\\")
        return fixedJson;

    }

    def storePdfVectors(params){
        def chapterId = params.chapterId
        if(params.resId==null){
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(""+chapterId),"Notes", [sort: "id", order: "asc"])
            if(readingMaterials.size()>0)
                params.put("resId",""+readingMaterials[0].id)
            else {
                def json = [status: "Error", message: "No PDF found for this chapter"]
                return json
            }
        }
        def resId = params.resId
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))
        if (documentInstance == null) {
            def json = [status: "Error", message: "Document not found."]
            return json

        } else {
            try {
                String namespace
                int resCode
                if(documentInstance.vectorStored==null||documentInstance.extractPath==null||"".equals(documentInstance.extractPath)) {
                    println("have to create vectors " + chapterId)
                    File pdfFile = new File(grailsApplication.config.grails.basedir.path + "/" + documentInstance.resLink)
                    if(!pdfFile.exists()){
                        println("pdf file does not exist for chapter id " + chapterId)
                       // get the content of the directory and see if there is any pdf file in it. if yes, then use that file.
                        File dir = new File(grailsApplication.config.grails.basedir.path + "/" + documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf("/")))
                        dir.listFiles().each { file ->
                            if(file.name.endsWith(".pdf")){
                                println("found pdf file " + file.name+" for chapter id " + chapterId)
                                documentInstance.resLink = documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf("/"))+"/"+file.name
                                documentInstance.save(failOnError: true, flush: true)
                                pdfFile = file
                            }
                        }

                    }


                    String index = promptService.getIndex("users")
                    namespace = index + "_" + chapterId + "_" + resId
                    String filePath = documentInstance.resLink
                    resCode = newUserPDFCreation( filePath, namespace,params.serverIPAddress)

                }else{
                    namespace = documentInstance.vectorStored
                    resCode = 200
                }
                def res = ["status":"OK","message":"PDF Vectors stored successfully","resCode":resCode,namespace: namespace,resId:documentInstance.id]
                return res
            }catch(Exception e){
                println("Exception in storePdfVectors: for chapter " + chapterId + " and the exception is " + e.toString())
                def err = ["status":"Error","message":e.message]
                return err
            }
        }
    }

    def newUserPDFCreation(filePath,namespace,String serverIPAddress){
        URL url = new URL("http://"+serverIPAddress+":8000/api"+"/processPDFVectorNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

        def json = new JsonBuilder([namespace: namespace, filePath: filePath])
        writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        return responseCode
    }





    @Transactional(propagation = Propagation.REQUIRES_NEW)

    int getNextChapterForProcessing(int serverIndex){

        KeyValueMst runGPTJob = KeyValueMst.findByKeyName("runGPTJob")
        if(runGPTJob!=null&&runGPTJob.keyValue.equals("true")) {
            int noOfParallelTasks = 3
            KeyValueMst keyValueMst = KeyValueMst.findByKeyName("numberOfParallelAutoGPTTasks")
            if (keyValueMst != null) {
                noOfParallelTasks = Integer.parseInt(keyValueMst.keyValue)
            }
            def autogptLogs = AutogptLog.findAllByGptStatusAndServerIndex("running", serverIndex)
            if (autogptLogs.size() < noOfParallelTasks) {
                String sql = " SELECT id " +
                        " FROM wslog.autogpt_log " +
                        " WHERE gpt_status = 'vectorCreated' " +
                        " ORDER BY CASE " +
                        "   WHEN higher_priority = 'true' THEN 1 " +
                        "   WHEN higher_priority = 'false' THEN 2 " +
                        "   ELSE 3 END, id ASC " +
                        " LIMIT 1";
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                def sql1 = new SafeSql(dataSource)
                def results = sql1.rows(sql)
                 if(results.size()>0){
                    AutogptLog autogptLog = AutogptLog.findById(new Long(""+results[0].id))
                    autogptLog.gptStatus = "running"
                    autogptLog.dateStarted = new Date()
                    autogptLog.dateCompleted = null
                    autogptLog.serverIndex = serverIndex
                    autogptLog.save(failOnError: true, flush: true)
                    return autogptLog.chapterId.intValue()
                }
                else {
                    return 0
                }
            }
            else {
                return 0
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)

    int getNextChapterForPDFProcessing(int serverIndex){

        KeyValueMst runGPTJob = KeyValueMst.findByKeyName("runGPTJob")
        if(runGPTJob!=null&&runGPTJob.keyValue.equals("true")) {

               AutogptLog autogptLog = AutogptLog.findByGptStatusAndServerIndex("vectorCreating", serverIndex)
               if(autogptLog==null) {

                   String sql = " SELECT id " +
                           " FROM wslog.autogpt_log " +
                           " WHERE gpt_status IS NULL " +
                           " ORDER BY CASE " +
                           "   WHEN higher_priority = 'true' THEN 1 " +
                           "   WHEN higher_priority = 'false' THEN 2 " +
                           "   ELSE 3 END, id ASC " +
                           " LIMIT 1";
                   def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                   def sql1 = new SafeSql(dataSource)
                   def results = sql1.rows(sql)
                   if (results.size() > 0) {
                       autogptLog = AutogptLog.findById(new Long("" + results[0].id))
                       autogptLog.gptStatus = "vectorCreating"
                       autogptLog.dateStarted = new Date()
                       autogptLog.serverIndex = serverIndex
                       autogptLog.save(failOnError: true, flush: true)
                       return autogptLog.chapterId.intValue()
                   } else {
                       return 0
                   }

               }else{
                   return 0
               }
        }
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def deleteEmbeddings(ResourceDtl resourceDtl) {
        if (resourceDtl.vectorStored != null) {
            JSONObject requestBody = new JSONObject()

            requestBody.put("namespace", resourceDtl.vectorStored)
             URL url = new URL(grailsApplication.config.grails.aiserver.url + "/delete-namespace")

            HttpURLConnection connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)
            connection.connect()
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            writer.write(requestBody.toString())
            writer.flush()
            writer.close()
            def responseCode = connection.getResponseCode()
            if (responseCode == 200) {
                resourceDtl.vectorStored = null
                resourceDtl.save(failOnError: true, flush: true)
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = reader.readLine()
                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            } else {
                return null
            }

        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def checkPendingJobs(){
        String sql  = " SELECT id " +
                " FROM wslog.autogpt_log\n" +
                " WHERE TIMESTAMPDIFF(HOUR, date_started, SYSDATE()) > 3\n" +
                " and gpt_status='running'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        results.each { log ->
            AutogptLog autogptLog = AutogptLog.findById(new Long(log.id))
            //delete
            autogptLog.delete(flush: true)
        }
    }

    def getChapterResources(String bookId){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if(booksMst.packageBookIds!=null) bookId = booksMst.packageBookIds

           List chapters = new ArrayList()
           String[] bookIds = bookId.split(",")
            for(int i=0;i<bookIds.length;i++){
                String tempBookId = bookIds[i]
                BooksDtl booksDtl = BooksDtl.findByBookId(new Integer(tempBookId))
                if(booksDtl!=null&&booksDtl.masterBookId!=null) tempBookId = ""+booksDtl.masterBookId
                chapters.addAll(ChaptersMst.findAllByBookId(new Integer(tempBookId)))
            }

        List chapterDetails = []
        chapters.each { chapter ->
            int noOfExercises = 0
            int noOfQB = 0
            int noOfQBMCQs = 0
            int noOfPYQs = 0
            String hasTheory = "No"

            ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","Exercise Solutions")
            if(resourceDtl!=null) {
                noOfExercises = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }

            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"Multiple Choice Questions","QuestionBank MCQs")
            if(resourceDtl!=null) {
                noOfQBMCQs = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }
            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","QuestionBank QnA")
            if(resourceDtl!=null) {
                noOfQB = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }

            //check if PYQs are created - separate count from Question Bank questions
            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","PYQs")
            if(resourceDtl!=null) {
                noOfPYQs = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }

            // Check if chapter file exists for Theory column
            // Logic from wpmain/getChapterContent - check if chapterFilePath exists
                String chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chapter.bookId + "/chapter_" + chapter.id + ".html"
                File chapterFile = new File(chapterFilePath)
                if (chapterFile.exists()) {
                    hasTheory = "Yes"
                }


            chapterDetails << [chapterId: chapter.id, chapterName: chapter.name, noOfExercises: noOfExercises, noOfQB: noOfQB, noOfQBMCQs: noOfQBMCQs, noOfPYQs: noOfPYQs, hasTheory: hasTheory]
        }
        return chapterDetails
    }

    def fixFormulas(String input) {
        return input
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def updateAutoGPTLog(Long chapterId,String gptStatus,String highPriority=null){
        AutogptLog autogptLog = AutogptLog.findByChapterId(chapterId)
        if(autogptLog!=null){
            autogptLog.gptStatus = gptStatus
            if(highPriority!=null) {
                autogptLog.higherPriority = highPriority
            }
            autogptLog.dateCompleted = new Date()
            autogptLog.save(failOnError: true, flush: true)
        }
    }

    def fixCurlyBrackets(String input) {
        if (!input) return input

        // Replace curly brackets that might interfere with JSON parsing
        return input
            .replace("{", "LEFTBRACE")
            .replace("}", "RIGHTBRACE")
    }

    def fixQuestion(String objId,String questionType){
        ObjectiveMst objectiveMst = ObjectiveMst.findById(new Integer(objId))
        Prompts prompts = Prompts.findByPromptType("questionFixer_"+questionType)


        String prompt
        if("mcq".equals(questionType)){
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n option1:\n"+objectiveMst.option1+"\n"+
                    "\n option2:\n"+objectiveMst.option2+"\n"+
                    "\n option3:\n"+objectiveMst.option3+"\n"+
                    "\n option4:\n"+objectiveMst.option4+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }else{
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+"\n"+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }

        def fixResponse = getQuestionFix(prompt, objId)
         def jsonResponse = new JsonSlurper().parseText( jsonCleaner(fixResponse.response))
        if(jsonResponse!=null){
            if("mcq".equals(questionType)){
                objectiveMst.question = jsonResponse.question
                objectiveMst.option1 = jsonResponse.option1
                objectiveMst.option2 = jsonResponse.option2
                objectiveMst.option3 = jsonResponse.option3
                objectiveMst.option4 = jsonResponse.option4
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.correctAnswer

                // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
                String correctAnswerStr = jsonResponse.correctAnswer.toString()
                objectiveMst.answer1 = (correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")) ? "Yes" : null
                objectiveMst.answer2 = (correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")) ? "Yes" : null
                objectiveMst.answer3 = (correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")) ? "Yes" : null
                objectiveMst.answer4 = (correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")) ? "Yes" : null
            }else{
                objectiveMst.question = jsonResponse.question
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.answer
            }

             objectiveMst.save(failOnError: true, flush: true)

        }
        return objectiveMst
    }

    def getQuestionFix(String questionInput,String objId){
        JSONObject requestBody = new JSONObject()
        requestBody.put("prompt", questionInput)
        URL url = new URL(grailsApplication.config.grails.aiserver.url+"/chat-completion")

        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            try{
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            }catch (Exception e){
                 println("Exception in getQuestionFix: "+e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                return null
            }

        }else{
            println("Error in getQuestionFix: "+responseCode)
            return null
        }

    }

    String fixJSONFromLLM(String input) {
       Prompts prompts = Prompts.findByPromptType("jsonFixer")
       String prompt = prompts.basePrompt + " \n "+input
        String output = getQuestionFix(prompt, "-1").response
        return output
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def runAutoGPT(int chapterId,String serverIPAddress) {
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(chapterId))
        println("runAutoGPT for chapter id " + chapterId + " on server " + serverIPAddress)
        def params = new HashMap()
        boolean theoryPresent=false
        String chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chaptersMst.bookId + "/chapter_" + chapterId + ".html"
        File chapterFile = new File(chapterFilePath)
        if (chapterFile.exists()) {
            theoryPresent = true
        }
        params.put("serverIPAddress", serverIPAddress)
        params.put("chapterId", "" + chapterId)
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes")
        if (resourceDtl != null) {
            params.put("resId", "" + resourceDtl.id)
        } else {
            def json = [status: "Error", message: "No PDF found for this chapter"]
            return json
        }
        println("Running getChapterMetaData for chapter id " + chapterId + " on server " + serverIPAddress)
        getChapterMetaData(params)
        AutogptLog autogptLog = AutogptLog.findByChapterId(chapterId)
        if("true".equals(autogptLog.createSnapshot)){
            if(!theoryPresent) {
                params.put("subjectSyllabus", autogptLog.subjectSyllabus)
                params.put("boardExam", autogptLog.boardExam)
                theoryBooksService.getPYQsForBoard(params)
            }
        }
        println("Running exerciseCollector for chapter id " + chapterId + " on server " + serverIPAddress)
        def response = exerciseCollector(params)
        println("exerciseCollector response: " + response+" for chapter id " + chapterId + " on server " + serverIPAddress)
        println("Running questionBankBuilder for chapter id " + chapterId + " on server " + serverIPAddress)
        def response1 = questionBankBuilder(params)
        println("questionBankBuilder response: " + response1+" for chapter id " + chapterId + " on server " + serverIPAddress)
        boolean restartRequired = false;
        String restartReason
        // let us check if the theory is created for the chapter
        if("true".equals(autogptLog.createSnapshot)){

                // Chapter content exists, get the content
                 chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chaptersMst.bookId + "/chapter_" + chapterId + ".html"
                 chapterFile = new File(chapterFilePath)
                if (!chapterFile.exists()) {
                    restartRequired = true
                    restartReason = "Theory not created"
                }else{
                    //check if PYQs is present
                    ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chapterId,"PYQs")
                    if(pyqResourceDtl==null) {
                        restartRequired = true;
                        restartReason = "PYQs not created"
                    }
                    else{
                        ObjectiveMst.withSession { session ->
                            session.clear()
                        }
                           //get number of questions which does not have answer
                        int noOfQuestionsWithoutAnswer = ObjectiveMst.countByQuizIdAndAnswerIsNull(new Integer(pyqResourceDtl.resLink))
                        if(noOfQuestionsWithoutAnswer>5) {
                            restartRequired = true;
                            restartReason = "PYQs not answered"
                        }else if(noOfQuestionsWithoutAnswer>0) {
                            restartRequired = false;
                            restartReason = "PYQs partially answered for chapter "+chapterId+" and no of questions without answer are "+noOfQuestionsWithoutAnswer
                        }
                    }
                };
        }

        println("restartRequired: for chapterId: "+chapterId+" is " + restartRequired+" reason: " + restartReason)
        if(restartRequired){
            updateAutoGPTLog(new Long("" + chapterId), "vectorCreated")
            //let us add the reason to the log
            autogptErrorLoggerService.createLog(new Long("" + chapterId), null, "runAutoGPT", "Restarting AutoGPT for chapter " + chapterId + " because " + restartReason, "")
        }else {
            deleteEmbeddings(resourceDtl)
            updateAutoGPTLog(new Long("" + chapterId), "completed")
        }
        def json = [status: "OK", message: "AutoGPT task completed"]
        return json
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def autoGPTPdfProcessorRunner(params) {
                def json =  storePdfVectors(params)
                updateAutoGPTLog(new Long("" + params.chapterId), "vectorCreated")

        return "Completed"
    }

    def fixLanguage(String objId,String questionType,String language){
        ObjectiveMst objectiveMst = ObjectiveMst.findById(new Integer(objId))
        Prompts prompts = Prompts.findByPromptType("languageCorrections_"+questionType)


        String prompt
        if("mcq".equals(questionType)){
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n option1:\n"+objectiveMst.option1+"\n"+
                    "\n option2:\n"+objectiveMst.option2+"\n"+
                    "\n option3:\n"+objectiveMst.option3+"\n"+
                    "\n option4:\n"+objectiveMst.option4+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }else{
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+"\n"+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }
        prompt = prompt.replaceAll("BOOKLANGUAGE", language)

        def fixResponse = getQuestionFix(prompt, objId)
        def jsonResponse = new JsonSlurper().parseText( jsonCleaner(fixResponse.response))
        if(jsonResponse!=null){
            if("mcq".equals(questionType)){
                objectiveMst.question = jsonResponse.question
                objectiveMst.option1 = jsonResponse.option1
                objectiveMst.option2 = jsonResponse.option2
                objectiveMst.option3 = jsonResponse.option3
                objectiveMst.option4 = jsonResponse.option4
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.correctAnswer

                // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
                String correctAnswerStr = jsonResponse.correctAnswer.toString()
                objectiveMst.answer1 = (correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")) ? "Yes" : null
                objectiveMst.answer2 = (correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")) ? "Yes" : null
                objectiveMst.answer3 = (correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")) ? "Yes" : null
                objectiveMst.answer4 = (correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")) ? "Yes" : null
            }else{
                objectiveMst.question = jsonResponse.question
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.answer
            }

            objectiveMst.save(failOnError: true, flush: true)

        }
        return objectiveMst
    }



}
